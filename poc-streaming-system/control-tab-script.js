/**
 * Control Tab Script for POC Streaming
 *
 * This script is injected into the control tab to manage WebRTC connections
 * and coordinate streaming between target tabs and web clients
 */

(function () {
  "use strict";

  console.log("[POC-Streaming] Control tab script initializing...");

  // Prevent multiple injections
  if (window.pocControlTabInjected) {
    console.log(
      "[POC-Streaming] Control tab script already injected, skipping..."
    );
    return;
  }
  window.pocControlTabInjected = true;

  class ControlTabManager {
    constructor() {
      this.signalingServerUrl = "ws://localhost:8080"; // Will be set dynamically
      this.websocket = null;
      this.isConnected = false;
      this.clientId = null;

      // WebRTC configuration
      this.rtcConfig = {
        iceServers: [
          { urls: "stun:stun.cloudflare.com:3478" },
          { urls: "stun:stun.l.google.com:19302" },
        ],
        iceCandidatePoolSize: 10,
      };

      // Connection management
      this.peerConnections = new Map(); // streamId -> RTCPeerConnection (to web client)
      this.targetConnections = new Map(); // streamId -> RTCPeerConnection (to target tab)
      this.targetTabs = new Map(); // tabId -> tabInfo
      this.pendingStreams = new Map(); // streamId -> { targetTabId, requestedBy, status }
      this.activeStreams = new Map(); // streamId -> { targetTabId, webClientId, peerConnection }

      this.init();
    }

    async init() {
      console.log("[POC-Streaming] Initializing control tab manager...");
      await this.connectToSignalingServer();
      this.setupPageListeners();
      this.createControlTabUI();
    }

    async connectToSignalingServer() {
      try {
        console.log("[POC-Streaming] Connecting to signaling server...");
        this.websocket = new WebSocket(this.signalingServerUrl);

        this.websocket.onopen = () => {
          console.log(
            "[POC-Streaming] Control tab connected to signaling server"
          );
          this.isConnected = true;

          // Register as control tab
          this.sendMessage({
            type: "register-control-tab",
            metadata: {
              userAgent: navigator.userAgent,
              timestamp: Date.now(),
            },
          });
        };

        this.websocket.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error("[POC-Streaming] Failed to parse message:", error);
          }
        };

        this.websocket.onclose = () => {
          console.log(
            "[POC-Streaming] Control tab disconnected from signaling server"
          );
          this.isConnected = false;
          this.scheduleReconnect();
        };

        this.websocket.onerror = (error) => {
          console.error("[POC-Streaming] Control tab WebSocket error:", error);
        };
      } catch (error) {
        console.error(
          "[POC-Streaming] Failed to connect to signaling server:",
          error
        );
        this.scheduleReconnect();
      }
    }

    scheduleReconnect() {
      console.log("[POC-Streaming] Scheduling reconnection in 5 seconds...");
      setTimeout(() => {
        this.connectToSignalingServer();
      }, 5000);
    }

    sendMessage(message) {
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        this.websocket.send(JSON.stringify(message));
      } else {
        console.warn("[POC-Streaming] Cannot send message - not connected");
      }
    }

    handleMessage(message) {
      console.log(
        "[POC-Streaming] Control tab received message:",
        message.type
      );

      switch (message.type) {
        case "welcome":
          this.handleWelcome(message);
          break;
        case "target-tabs-list":
          this.handleTargetTabsList(message);
          break;
        case "target-tab-registered":
          this.handleTargetTabRegistered(message);
          break;
        case "target-tab-disconnected":
          this.handleTargetTabDisconnected(message);
          break;
        case "stream-requested":
          // this.handleStreamRequested(message);
          break;
        case "stream-stopped":
          this.handleStreamStopped(message);
          break;
        case "webrtc-offer":
          this.handleWebRTCOffer(message);
          break;
        case "webrtc-answer":
          this.handleWebRTCAnswer(message);
          break;
        case "webrtc-ice-candidate":
          this.handleWebRTCIceCandidate(message);
          break;
        case "webrtc-offer-from-target":
          this.handleTargetTabOffer(message);
          break;
        case "webrtc-ice-candidate-from-target":
          this.handleTargetTabIceCandidate(message);
          break;
        default:
          console.log("[POC-Streaming] Unknown message type:", message.type);
      }
    }

    handleWelcome(message) {
      this.clientId = message.clientId;
      console.log(
        "[POC-Streaming] Control tab registered with ID:",
        this.clientId
      );
    }

    handleTargetTabsList(message) {
      console.log(
        "[POC-Streaming] Received target tabs list:",
        message.targetTabs.length
      );

      // Update target tabs
      this.targetTabs.clear();
      message.targetTabs.forEach((tab) => {
        this.targetTabs.set(tab.tabId, tab);
      });
    }

    handleTargetTabRegistered(message) {
      console.log("[POC-Streaming] Target tab registered:", message.tabId);
      this.targetTabs.set(message.tabId, message);
    }

    handleTargetTabDisconnected(message) {
      console.log("[POC-Streaming] Target tab disconnected:", message.tabId);
      this.targetTabs.delete(message.tabId);

      // Clean up any active streams for this tab
      for (const [streamId, stream] of this.activeStreams) {
        if (stream.targetTabId === message.tabId) {
          this.cleanupStream(streamId);
        }
      }
    }

    handleStreamStopped(message) {
      console.log("[POC-Streaming] Stream stopped:", message.streamId);
      this.cleanupStream(message.streamId);
    }

    async handleWebRTCOffer() {
      console.log("[POC-Streaming] Received WebRTC offer");
      // This would be handled if control tab receives offers (not typical in this architecture)
    }

    async handleWebRTCAnswer(message) {
      console.log(
        "[POC-Streaming] Received WebRTC answer for stream:",
        message.streamId
      );

      const peerConnection = this.peerConnections.get(message.streamId);
      if (peerConnection) {
        try {
          await peerConnection.setRemoteDescription(
            new RTCSessionDescription(message.answer)
          );
          console.log(
            "[POC-Streaming] WebRTC connection established for stream:",
            message.streamId
          );
        } catch (error) {
          console.error(
            "[POC-Streaming] Failed to set remote description:",
            error
          );
        }
      }
    }

    async handleWebRTCIceCandidate(message) {
      const peerConnection = this.peerConnections.get(message.streamId);
      if (peerConnection) {
        try {
          await peerConnection.addIceCandidate(message.candidate);
        } catch (error) {
          console.error("[POC-Streaming] Failed to add ICE candidate:", error);
        }
      }
    }

    async handleTargetTabOffer(message) {
      console.log(
        "[POC-Streaming] Received WebRTC offer from target tab:",
        message.streamId
      );
      console.log("[POC-Streaming] Target tab message:", message);

      // Create stream info from the message since target tab is initiating
      const streamInfo = {
        targetTabId: message.targetTabId,
        requestedBy: message.targetClientId,
        status: "connecting",
      };

      console.log("[POC-Streaming] Stream info created:", streamInfo);

      // Store the stream info
      this.pendingStreams.set(message.streamId, streamInfo);

      // Create peer connection to target tab
      const targetPeerConnection = new RTCPeerConnection(this.rtcConfig);

      // Create peer connection to web client
      const clientPeerConnection = new RTCPeerConnection(this.rtcConfig);

      // Store both connections
      this.targetConnections.set(message.streamId, targetPeerConnection);
      this.peerConnections.set(message.streamId, clientPeerConnection);

      // Handle incoming stream from target tab
      targetPeerConnection.ontrack = (event) => {
        console.log("[POC-Streaming] Received stream from target tab");
        console.log("[POC-Streaming] Target stream event:", event);
        const [stream] = event.streams;
        console.log("[POC-Streaming] Target stream:", stream);
        console.log(
          "[POC-Streaming] Target stream tracks:",
          stream.getTracks()
        );

        // Display the stream in control tab
        this.displayStreamInControlTab(message.streamId, stream, streamInfo);

        // Forward stream to web client
        stream.getTracks().forEach((track) => {
          console.log(
            "[POC-Streaming] Adding track to client peer connection:",
            track
          );
          clientPeerConnection.addTrack(track, stream);
        });

        // Create offer to web client
        this.createOfferToWebClient(message.streamId, streamInfo.requestedBy);
      };

      // Handle ICE candidates from target tab
      targetPeerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          // Send ICE candidate back to target tab
          this.sendMessage({
            type: "webrtc-ice-candidate-to-target",
            candidate: event.candidate,
            streamId: message.streamId,
            targetTabId: streamInfo.targetTabId,
          });
        }
      };

      // Handle ICE candidates from web client
      clientPeerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          this.sendMessage({
            type: "webrtc-ice-candidate",
            candidate: event.candidate,
            streamId: message.streamId,
            targetClientId: streamInfo.requestedBy,
          });
        }
      };

      // Accept the offer from target tab
      try {
        await targetPeerConnection.setRemoteDescription(
          new RTCSessionDescription(message.offer)
        );
        const answer = await targetPeerConnection.createAnswer();
        await targetPeerConnection.setLocalDescription(answer);

        // Send answer back to target tab
        this.sendMessage({
          type: "webrtc-answer-to-target",
          answer: answer,
          streamId: message.streamId,
          targetTabId: streamInfo.targetTabId,
        });

        console.log("[POC-Streaming] WebRTC answer sent to target tab");
      } catch (error) {
        console.error(
          "[POC-Streaming] Failed to handle target tab offer:",
          error
        );
      }
    }

    async handleTargetTabIceCandidate(message) {
      const targetPeerConnection = this.targetConnections.get(message.streamId);
      if (targetPeerConnection) {
        try {
          await targetPeerConnection.addIceCandidate(message.candidate);
        } catch (error) {
          console.error(
            "[POC-Streaming] Failed to add target ICE candidate:",
            error
          );
        }
      }
    }

    async createOfferToWebClient(streamId, webClientId) {
      const peerConnection = this.peerConnections.get(streamId);
      if (!peerConnection) {
        console.error(
          "[POC-Streaming] No peer connection found for stream:",
          streamId
        );
        return;
      }

      try {
        const offer = await peerConnection.createOffer();
        await peerConnection.setLocalDescription(offer);

        // Send offer to web client
        this.sendMessage({
          type: "webrtc-offer",
          offer: offer,
          targetClientId: webClientId,
          streamId: streamId,
          fromClientId: this.clientId,
        });

        console.log(
          "[POC-Streaming] WebRTC offer sent to web client for stream:",
          streamId
        );
      } catch (error) {
        console.error(
          "[POC-Streaming] Failed to create offer to web client:",
          error
        );
      }
    }

    async createDummyStream() {
      // Create a canvas with animated content as a placeholder
      const canvas = document.createElement("canvas");
      canvas.width = 640;
      canvas.height = 480;
      const ctx = canvas.getContext("2d");

      // Create animated content
      let frame = 0;
      const animate = () => {
        ctx.fillStyle = `hsl(${frame % 360}, 50%, 50%)`;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.fillStyle = "white";
        ctx.font = "48px Arial";
        ctx.textAlign = "center";
        ctx.fillText("POC Stream", canvas.width / 2, canvas.height / 2);
        ctx.fillText(
          `Frame: ${frame}`,
          canvas.width / 2,
          canvas.height / 2 + 60
        );

        frame++;
        requestAnimationFrame(animate);
      };
      animate();

      // Get stream from canvas
      const stream = canvas.captureStream(30); // 30 FPS

      // Add audio track (silent)
      const audioContext = new AudioContext();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      gainNode.gain.value = 0; // Silent
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      const audioStream = audioContext.createMediaStreamDestination();
      oscillator.connect(audioStream);
      oscillator.start();

      // Combine video and audio
      const combinedStream = new MediaStream([
        ...stream.getVideoTracks(),
        ...audioStream.stream.getAudioTracks(),
      ]);

      return combinedStream;
    }

    cleanupStream(streamId) {
      console.log("[POC-Streaming] Cleaning up stream:", streamId);

      const peerConnection = this.peerConnections.get(streamId);
      if (peerConnection) {
        peerConnection.close();
        this.peerConnections.delete(streamId);
      }

      const targetConnection = this.targetConnections.get(streamId);
      if (targetConnection) {
        targetConnection.close();
        this.targetConnections.delete(streamId);
      }

      // Remove from UI
      const streamElement = document.getElementById(`poc-stream-${streamId}`);
      if (streamElement) {
        streamElement.remove();
      }

      // Add "no streams" message if no streams left
      const streamsContainer = document.getElementById("poc-streams-container");
      if (streamsContainer && streamsContainer.children.length === 0) {
        streamsContainer.innerHTML =
          '<div style="color: #666; font-style: italic;">No active streams</div>';
      }

      this.activeStreams.delete(streamId);
    }

    createControlTabUI() {
      // Create a floating control panel for the control tab
      const controlPanel = document.createElement("div");
      controlPanel.id = "poc-control-panel";
      controlPanel.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        width: 400px;
        max-height: 600px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        border-radius: 8px;
        padding: 16px;
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        overflow-y: auto;
      `;

      controlPanel.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
          <h3 style="margin: 0; color: #4CAF50;">POC Control Tab</h3>
          <button id="poc-toggle-panel" style="background: none; border: 1px solid #666; color: white; padding: 4px 8px; border-radius: 4px; cursor: pointer;">−</button>
        </div>
        <div id="poc-panel-content">
          <div style="margin-bottom: 12px;">
            <div style="font-weight: bold; margin-bottom: 4px;">Status:</div>
            <div id="poc-connection-status" style="color: #ff9800;">Connecting...</div>
          </div>
          <div style="margin-bottom: 12px;">
            <div style="font-weight: bold; margin-bottom: 4px;">Active Streams:</div>
            <div id="poc-streams-container" style="max-height: 300px; overflow-y: auto;">
              <div style="color: #666; font-style: italic;">No active streams</div>
            </div>
          </div>
          <div style="margin-bottom: 12px;">
            <div style="font-weight: bold; margin-bottom: 4px;">Target Tabs:</div>
            <div id="poc-tabs-container" style="max-height: 200px; overflow-y: auto;">
              <div style="color: #666; font-style: italic;">No target tabs</div>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(controlPanel);

      // Add toggle functionality
      const toggleBtn = document.getElementById("poc-toggle-panel");
      const panelContent = document.getElementById("poc-panel-content");
      let isCollapsed = false;

      toggleBtn.addEventListener("click", () => {
        isCollapsed = !isCollapsed;
        panelContent.style.display = isCollapsed ? "none" : "block";
        toggleBtn.textContent = isCollapsed ? "+" : "−";
        controlPanel.style.height = isCollapsed ? "auto" : "";
      });

      // Update connection status
      this.updateConnectionStatus("Connected");
    }

    updateConnectionStatus(status) {
      const statusElement = document.getElementById("poc-connection-status");
      if (statusElement) {
        statusElement.textContent = status;
        statusElement.style.color =
          status === "Connected" ? "#4CAF50" : "#ff9800";
      }
    }

    displayStreamInControlTab(streamId, mediaStream, streamInfo) {
      console.log(
        "[POC-Streaming] Displaying stream in control tab:",
        streamId
      );

      const streamsContainer = document.getElementById("poc-streams-container");
      if (!streamsContainer) {
        console.warn("[POC-Streaming] Streams container not found");
        return;
      }

      // Remove "no streams" message if present
      const noStreamsMsg = streamsContainer.querySelector(
        '[style*="font-style: italic"]'
      );
      if (
        noStreamsMsg &&
        noStreamsMsg.textContent.includes("No active streams")
      ) {
        noStreamsMsg.remove();
      }

      // Create stream display element
      const streamElement = document.createElement("div");
      streamElement.id = `poc-stream-${streamId}`;
      streamElement.style.cssText = `
        margin-bottom: 12px;
        padding: 8px;
        border: 1px solid #333;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.05);
      `;

      streamElement.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 8px; color: #4CAF50;">
          📺 Stream ${streamId.substring(0, 8)}...
        </div>
        <video
          id="poc-video-${streamId}"
          autoplay
          muted
          playsinline
          style="width: 100%; height: 150px; background: #000; border-radius: 4px; object-fit: contain;"
        ></video>
        <div style="margin-top: 8px; font-size: 12px; color: #ccc;">
          Target: ${streamInfo.targetTabId || "Unknown"}
        </div>
      `;

      streamsContainer.appendChild(streamElement);

      // Set the video source
      const video = document.getElementById(`poc-video-${streamId}`);
      if (video && mediaStream) {
        video.srcObject = mediaStream;

        video.onloadedmetadata = () => {
          console.log("[POC-Streaming] Video metadata loaded in control tab");
        };

        video.onplay = () => {
          console.log("[POC-Streaming] Video started playing in control tab");
        };

        video.onerror = (error) => {
          console.error("[POC-Streaming] Video error in control tab:", error);
        };
      }
    }

    setupPageListeners() {
      // Listen for page unload
      window.addEventListener("beforeunload", () => {
        console.log("[POC-Streaming] Control tab unloading...");

        // Clean up all streams
        for (const streamId of this.activeStreams.keys()) {
          this.cleanupStream(streamId);
        }

        // Close WebSocket
        if (this.websocket) {
          this.websocket.close();
        }
      });
    }
  }

  // Initialize the control tab manager
  window.pocControlTabManager = new ControlTabManager();

  console.log("[POC-Streaming] Control tab script initialized successfully");
})();
